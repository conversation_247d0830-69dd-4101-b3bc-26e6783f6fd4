import request from './axios';

/**
 * 获取课程列表
 * @returns {Promise<Array>} 课程列表
 */
export const getCourseList = () => {
  console.log('正在获取课程列表...');
  return request({
    url: '/core/zstp/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 100 // 增加默认获取数量到100个
    }
  }).then(response => {
    console.log('获取课程列表成功:', response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准响应格式');
        return response;
      } else if (Array.isArray(response)) {
        console.log('返回数组格式');
        return { rows: response };
      } else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取课程列表数据');
        return { rows: response.data };
      }
    }
    console.warn('响应格式不符合预期，返回模拟数据');
    return { rows: getMockCourses() };
  }).catch(error => {
    console.error('获取课程列表失败:', error);
    console.log('返回模拟课程数据');
    // 如果API未就绪，返回模拟数据
    return { rows: getMockCourses() };
  });
};

/**
 * 获取课程大纲
 * @param {string} courseId 课程ID
 * @returns {Promise<Object>} 课程大纲数据
 */
export const getCourseOutline = (courseId) => {
  console.log(`正在获取课程大纲: ${courseId}`);
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'get'
  }).then(response => {
    console.log(`获取课程${courseId}大纲成功:`, response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.success && response.data) {
        console.log('从嵌套响应中提取大纲数据');
        return response.data;
      } else if (response.nodes || response.title) {
        return response;
      }
    }
    console.warn('响应格式不符合预期，返回默认大纲');
    return getMockCourseOutline(courseId);
  }).catch(error => {
    console.error(`获取课程 ${courseId} 大纲失败:`, error);
    console.log('返回模拟课程大纲数据');
    // 如果API未就绪，返回模拟数据
    return getMockCourseOutline(courseId);
  });
};

/**
 * 获取课程知识图谱
 * @param {string} courseId 课程ID
 * @returns {Promise<Object>} 课程知识图谱数据
 */
export const getCourseGraph = (courseId) => {
  console.log(`正在获取节点数据: ${courseId}`);
  return request({
    url: `core/node/list`,
    method: 'get',
    params: {
      graphId: courseId
    }
  }).then(response => {
    console.log(`获取节点数据:`, response);
    return response.rows;
  });
};

export const getNodeStyle=(nodeId)=>{
  return request({
    url: `core/style/${nodeId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取节点样式:`, response);
    return response.data;
  });
}

export const getNodeLines=(nodeId)=>{
  return request({
    url: `core/line/list`,
    method: 'get',
    params: {
      nodeId: nodeId
    }
  }).then(response=>{
    console.log(`获取节点线条:`, response);
    return response.rows;
  });
}

/**
 * 保存课程知识图谱
 * @param {string} courseId 课程ID
 * @param {Object} data 知识图谱数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseGraph = (courseId, data) => {
  // 将前端格式转换为后端所需的数据结构
  const requestData = {
    graph: {
      id: data.graphId || courseId,
      name: data.name || '未命名图谱',
      content: data.content || ''
    },
    nodes: data.nodes.map(node => ({
      id: node.id && !node.id.includes('node') ? Number(node.id) : null, // 如果是新节点(id格式如'node1')则不发送id
      graph_id: data.graphId || courseId,
      parent_id: node.parentId ? Number(node.parentId) : null,
      name: node.text || '',
      content: node.content || ''
    })),
    lines: data.links.map(link => ({
      id: link.id && !link.id.includes('line') ? Number(link.id) : null,
      node_id: Number(link.from),
      target_id: Number(link.to),
      content: link.text || ''
    })),
    nodeStyles: data.nodes.map(node => ({
      node_id: Number(node.id),
      type: node.type || '',
      color: node.color || '',
      fontColor: node.fontColor || '',
      node_shape: node.nodeShape || 0,
      width: node.width || null,
      height: node.height || null,
      border_width: node.borderWidth || null,
      border_height: node.borderHeight || null,
      fixed: node.fixed || '',
      x: node.x || null,
      y: node.y || null
    }))
  };

  return request({
    url: `/api/graph/${courseId}`,
    method: 'post',
    data: requestData
  }).catch(error => {
    console.error(`保存课程 ${courseId} 知识图谱失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存课程大纲
 * @param {string} courseId 课程ID
 * @param {Object} data 大纲数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveCourseOutline = (courseId, data) => {
  return request({
    url: `/api/courses/${courseId}/outline`,
    method: 'post',
    data
  }).catch(error => {
    console.error(`保存课程 ${courseId} 大纲失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 从大纲创建知识图谱
 * @param {string} courseId 课程ID
 * @returns {Promise<Object>} 知识图谱数据
 */
export const createGraphFromOutline = (courseId) => {
  console.log(`从大纲创建知识图谱: ${courseId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-to-graph`,
    method: 'post'
  }).catch(error => {
    console.error(`从大纲创建知识图谱失败: ${courseId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 从知识图谱更新大纲
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 更新结果
 */
export const updateOutlineFromGraph = (graphId) => {
  console.log(`从知识图谱更新大纲: ${graphId}`);
  return request({
    url: `/api/graph/graphs/${graphId}/graph-to-outline`,
    method: 'post'
  }).catch(error => {
    console.error(`从知识图谱更新大纲失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 同步节点映射关系
 * @param {string} courseId 课程ID
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 同步结果
 */
export const syncNodeMappings = (courseId, graphId) => {
  console.log(`同步节点映射关系: 课程${courseId}, 图谱${graphId}`);
  return request({
    url: `/api/graph/courses/${courseId}/graphs/${graphId}/sync-mappings`,
    method: 'post'
  }).catch(error => {
    console.error(`同步节点映射关系失败: 课程${courseId}, 图谱${graphId}`, error);
    return Promise.reject(error);
  });
};


export const deleteNode=(nodeId)=>{
  return request({
    url: `core/node/${nodeId}`,
    method: 'delete',
    
  }).then(response=>{
    console.log(`删除节点:`, response);    return response.data;
  });
}

export const createNode=(node)=>{
  return request({
    url: `core/node`,
    method: 'post',
    data: node
  }).then(response=>{
    console.log(`创建节点:`, response);
    return response.data;
  });
}

export const updateNode=(node)=>{
  return request({
    url: `core/node`,
    method: 'put',
    data: node
  }).then(response=>{
    console.log(`更新节点:`, response);
    return response.data;
  });
}



/**
 * 获取大纲节点关联的知识点
 * @param {string} courseId 课程ID
 * @param {string} nodeId 大纲节点ID
 * @returns {Promise<Array>} 关联的知识点列表
 */
export const getKnowledgeNodesForOutlineNode = (courseId, nodeId) => {
  console.log(`获取大纲节点关联的知识点: 课程${courseId}, 节点${nodeId}`);
  return request({
    url: `/api/outline/courses/${courseId}/outline-nodes/${nodeId}/knowledge-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取大纲节点关联的知识点失败: 课程${courseId}, 节点${nodeId}`, error);
    return [];
  });
};

/**
 * 获取知识点关联的大纲节点
 * @param {string} nodeId 知识点ID
 * @returns {Promise<Array>} 关联的大纲节点列表
 */
export const getOutlineNodesForKnowledgeNode = (nodeId) => {
  console.log(`获取知识点关联的大纲节点: 知识点${nodeId}`);
  return request({
    url: `/api/graph/knowledge-nodes/${nodeId}/outline-nodes`,
    method: 'get'
  }).then(response => {
    if (response && response.success && Array.isArray(response.data)) {
      return response.data;
    }
    return response || [];
  }).catch(error => {
    console.error(`获取知识点关联的大纲节点失败: 知识点${nodeId}`, error);
    return [];
  });
};

/**
 * 获取模拟课程列表数据
 * @returns {Array} 课程列表
 */
const getMockCourses = () => {
  return [
    {
      id: 'civil-surveying',
      name: '土木工程测量',
      description: '土木工程测量基础知识和技能',
      nodeCount: 45,
      createdAt: '2023-01-15T08:00:00Z',
      updatedAt: '2023-05-20T14:30:00Z'
    },
    {
      id: 'civil-intro',
      name: '土木工程概论',
      description: '土木工程基本概念和原理介绍',
      nodeCount: 38,
      createdAt: '2023-02-10T10:15:00Z',
      updatedAt: '2023-06-05T16:45:00Z'
    },
    {
      id: 'structural-analysis',
      name: '结构力学',
      description: '结构力学基本原理和分析方法',
      nodeCount: 52,
      createdAt: '2023-03-05T09:30:00Z',
      updatedAt: '2023-06-15T11:20:00Z'
    },
    {
      id: 'construction-materials',
      name: '建筑材料',
      description: '建筑材料的性质和应用',
      nodeCount: 32,
      createdAt: '2023-03-25T13:45:00Z',
      updatedAt: '2023-05-28T15:10:00Z'
    }
  ];
};

/**
 * 获取模拟课程大纲数据
 * @param {string} courseId 课程ID
 * @returns {Object} 课程大纲数据
 */
const getMockCourseOutline = (courseId) => {
  const outlines = {
    'civil-surveying': {
      id: 'civil-surveying',
      title: '土木工程测量',
      nodes: [
        {
          id: '1',
          title: '测量学基础',
          content: '介绍测量学的基本概念、历史发展和在土木工程中的应用。',
          children: [
            {
              id: '1.1',
              title: '测量学概述',
              content: '测量学的定义、分类、发展历程以及在工程建设中的重要性。',
              children: []
            },
            {
              id: '1.2',
              title: '基本测量原理',
              content: '测量的基本原理、误差理论、精度评定和测量方法简介。',
              children: []
            },
            {
              id: '1.3',
              title: '常用测量仪器',
              content: '水准仪、经纬仪、全站仪、GPS接收机等常用测量仪器的构造和使用方法。',
              children: [
                {
                  id: '1.3.1',
                  title: '水准仪',
                  content: '水准仪的构造、检验、校正及使用方法。',
                  children: []
                },
                {
                  id: '1.3.2',
                  title: '经纬仪',
                  content: '经纬仪的构造、检验、校正及使用方法。',
                  children: []
                }
              ]
            }
          ]
        },
        {
          id: '2',
          title: '水准测量',
          content: '介绍水准测量的方法、原理和应用。',
          children: [
            {
              id: '2.1',
              title: '水准测量原理',
              content: '高程系统、水准测量的基本原理和方法。',
              children: []
            },
            {
              id: '2.2',
              title: '水准路线测量',
              content: '水准路线测量的方法、误差分析和平差计算。',
              children: []
            },
            {
              id: '2.3',
              title: '工程水准测量',
              content: '建筑施工中的水准测量应用，包括放样、沉降观测等。',
              children: []
            }
          ]
        },
        {
          id: '3',
          title: '角度测量',
          content: '介绍角度测量的原理、方法和应用。',
          children: [
            {
              id: '3.1',
              title: '水平角测量',
              content: '水平角的测量方法、误差分析及精度控制。',
              children: []
            },
            {
              id: '3.2',
              title: '竖直角测量',
              content: '竖直角的测量方法、误差分析及应用。',
              children: []
            }
          ]
        },
        {
          id: '4',
          title: '距离测量',
          content: '介绍距离测量的原理、方法和应用。',
          children: [
            {
              id: '4.1',
              title: '直接测距',
              content: '钢尺量距的方法、误差分析及精度控制。',
              children: []
            },
            {
              id: '4.2',
              title: '光电测距',
              content: '全站仪、激光测距仪等光电测距仪器的使用方法与精度分析。',
              children: []
            },
            {
              id: '4.3',
              title: 'GPS测距',
              content: 'GPS测量基本原理及在距离测量中的应用。',
              children: []
            }
          ]
        },
        {
          id: '5',
          title: '控制测量',
          content: '介绍工程控制测量的方法和应用。',
          children: [
            {
              id: '5.1',
              title: '平面控制测量',
              content: '三角测量、导线测量等平面控制网的建立方法与计算。',
              children: []
            },
            {
              id: '5.2',
              title: '高程控制测量',
              content: '水准网的建立方法、观测方案和平差计算。',
              children: []
            },
            {
              id: '5.3',
              title: 'GPS控制测量',
              content: 'GPS控制网的设计、观测和数据处理。',
              children: []
            }
          ]
        },
        {
          id: '6',
          title: '地形测量',
          content: '介绍地形图测绘的方法和应用。',
          children: [
            {
              id: '6.1',
              title: '地形图概述',
              content: '地形图的分类、比例尺、图式和精度要求。',
              children: []
            },
            {
              id: '6.2',
              title: '地形图测绘方法',
              content: '全站仪法、GPS-RTK法、无人机摄影测量等地形图测绘方法。',
              children: []
            },
            {
              id: '6.3',
              title: '数字地形模型',
              content: '数字高程模型(DEM)、三角网模型(TIN)等数字地形表达方法。',
              children: []
            }
          ]
        },
        {
          id: '7',
          title: '工程放样',
          content: '介绍土木工程建设中的放样测量方法和应用。',
          children: [
            {
              id: '7.1',
              title: '放样准备与基本方法',
              content: '放样数据准备、放样控制网建立及基本放样方法。',
              children: []
            },
            {
              id: '7.2',
              title: '建筑工程放样',
              content: '建筑轴线、基础、楼层等放样方法与精度控制。',
              children: []
            },
            {
              id: '7.3',
              title: '道路工程放样',
              content: '道路中线、横断面等放样方法与应用。',
              children: []
            }
          ]
        },
        {
          id: '8',
          title: '变形监测',
          content: '介绍工程结构变形监测的方法和应用。',
          children: [
            {
              id: '8.1',
              title: '变形监测基本概念',
              content: '变形监测的目的、意义和基本方法。',
              children: []
            },
            {
              id: '8.2',
              title: '沉降观测',
              content: '建筑物沉降观测的方法、数据处理和成果分析。',
              children: []
            },
            {
              id: '8.3',
              title: '水平位移观测',
              content: '大坝、高层建筑等结构水平位移观测方法与数据分析。',
              children: []
            }
          ]
        }
      ]
    },
    'civil-intro': {
      id: 'civil-intro',
      title: '土木工程概论',
      nodes: [
        {
          id: '1',
          title: '土木工程概述',
          content: '介绍土木工程的定义、历史发展和现代土木工程的范畴。',
          children: [
            {
              id: '1.1',
              title: '土木工程的定义与分类',
              content: '土木工程的基本定义、主要分支及其在国民经济中的地位和作用。',
              children: []
            },
            {
              id: '1.2',
              title: '土木工程发展简史',
              content: '从古至今土木工程的发展历程、重要里程碑及杰出代表人物。',
              children: []
            },
            {
              id: '1.3',
              title: '现代土木工程的特点',
              content: '现代土木工程的跨学科特点、创新趋势及面临的挑战。',
              children: []
            }
          ]
        },
        {
          id: '2',
          title: '结构工程基础',
          content: '介绍结构工程的基本概念、结构类型及其设计原则。',
          children: [
            {
              id: '2.1',
              title: '结构的基本概念',
              content: '力、受力分析、内力、应力与变形等基本结构力学概念。',
              children: []
            },
            {
              id: '2.2',
              title: '常见结构类型',
              content: '桁架、梁、拱、框架、网壳等常见结构类型的特点及应用。',
              children: []
            },
            {
              id: '2.3',
              title: '结构设计原则',
              content: '安全性、适用性、耐久性、经济性和可持续性等结构设计的基本原则。',
              children: []
            }
          ]
        },
        {
          id: '3',
          title: '建筑材料基础',
          content: '介绍土木工程中常用的建筑材料及其性能特点。',
          children: [
            {
              id: '3.1',
              title: '混凝土',
              content: '混凝土的组成、性能、配合比设计及施工工艺。',
              children: []
            },
            {
              id: '3.2',
              title: '钢材',
              content: '钢材的类型、性能及在工程中的应用。',
              children: []
            },
            {
              id: '3.3',
              title: '砌体材料',
              content: '砖、砌块等砌体材料的特性及应用。',
              children: []
            },
            {
              id: '3.4',
              title: '木材与其他材料',
              content: '木材、玻璃、高分子材料等其他建筑材料的特性及应用。',
              children: []
            }
          ]
        },
        {
          id: '4',
          title: '岩土工程基础',
          content: '介绍岩土工程的基本概念、土壤性质及基础类型。',
          children: [
            {
              id: '4.1',
              title: '土壤的基本性质',
              content: '土的物理性质、工程分类及力学特性。',
              children: []
            },
            {
              id: '4.2',
              title: '地基承载力',
              content: '地基承载力的概念、影响因素及计算方法。',
              children: []
            },
            {
              id: '4.3',
              title: '常见基础类型',
              content: '浅基础、深基础的类型、适用条件及设计要点。',
              children: []
            }
          ]
        },
        {
          id: '5',
          title: '水利工程基础',
          content: '介绍水利工程的基本概念、常见水工建筑物及防洪工程。',
          children: [
            {
              id: '5.1',
              title: '水文基础',
              content: '水文循环、降雨-径流关系等水文学基础知识。',
              children: []
            },
            {
              id: '5.2',
              title: '水工建筑物',
              content: '大坝、水闸、渠道等常见水工建筑物的类型与功能。',
              children: []
            },
            {
              id: '5.3',
              title: '防洪工程',
              content: '防洪标准、防洪措施及洪水风险管理。',
              children: []
            }
          ]
        },
        {
          id: '6',
          title: '交通工程基础',
          content: '介绍道路、桥梁、隧道等交通工程的基本知识。',
          children: [
            {
              id: '6.1',
              title: '道路工程',
              content: '道路等级、几何设计、路基路面结构等基本知识。',
              children: []
            },
            {
              id: '6.2',
              title: '桥梁工程',
              content: '桥梁的类型、组成及设计要点。',
              children: []
            },
            {
              id: '6.3',
              title: '隧道工程',
              content: '隧道的类型、施工方法及安全措施。',
              children: []
            }
          ]
        },
        {
          id: '7',
          title: '施工技术与管理',
          content: '介绍土木工程施工的基本技术和项目管理知识。',
          children: [
            {
              id: '7.1',
              title: '施工组织设计',
              content: '施工方案规划、资源配置、进度控制等施工组织设计内容。',
              children: []
            },
            {
              id: '7.2',
              title: '常见施工技术',
              content: '土方工程、基础工程、主体结构及装饰装修等施工技术。',
              children: []
            },
            {
              id: '7.3',
              title: '工程项目管理',
              content: '工程质量、安全、进度、成本等方面的控制与管理。',
              children: []
            }
          ]
        },
        {
          id: '8',
          title: '土木工程与可持续发展',
          content: '介绍土木工程与可持续发展的关系及绿色建筑理念。',
          children: [
            {
              id: '8.1',
              title: '绿色建筑',
              content: '绿色建筑的概念、评价体系及设计策略。',
              children: []
            },
            {
              id: '8.2',
              title: '节能减排技术',
              content: '建筑节能、可再生能源利用等减少碳排放的技术措施。',
              children: []
            },
            {
              id: '8.3',
              title: '生态工程',
              content: '生态工程的理念及在土木工程中的应用。',
              children: []
            }
          ]
        }
      ]
    },
    'default': {
      id: 'default',
      title: '默认知识图谱',
      nodes: [
        {
          id: '1',
          title: '一级知识点1',
          content: '这是一级知识点1的详细内容...',
          children: [
            {
              id: '1.1',
              title: '二级知识点1.1',
              content: '这是二级知识点1.1的详细内容...',
              children: []
            }
          ]
        },
        {
          id: '2',
          title: '一级知识点2',
          content: '这是一级知识点2的详细内容...',
          children: []
        }
      ]
    }
  };

  return outlines[courseId] || outlines['default'];
};

/**
 * 获取模拟课程知识图谱数据
 * @param {string} courseId 课程ID
 * @returns {Object} 课程知识图谱数据
 */
const getMockCourseGraph = (courseId) => {
  const graphs = {
    'civil-surveying': {
      rootId: '1',
      nodes: [
        { 
          id: '1', 
          text: '测量学基础',
          content: '介绍测量学的基本概念、历史发展和在土木工程中的应用。',
          category: 'root'
        },
        { 
          id: '2', 
          text: '测量学概述',
          content: '测量学的定义、分类、发展历程以及在工程建设中的重要性。',
          category: 'concept'
        },
        { 
          id: '3', 
          text: '基本测量原理',
          content: '测量的基本原理、误差理论、精度评定和测量方法简介。',
          category: 'principle'
        },
        { 
          id: '4', 
          text: '常用测量仪器',
          content: '水准仪、经纬仪、全站仪、GPS接收机等常用测量仪器的构造和使用方法。',
          category: 'tool'
        },
        { 
          id: '5', 
          text: '水准测量',
          content: '介绍水准测量的方法、原理和应用。',
          category: 'method'
        },
        { 
          id: '6', 
          text: '角度测量',
          content: '介绍角度测量的原理、方法和应用。',
          category: 'method'
        },
        { 
          id: '7', 
          text: '距离测量',
          content: '介绍距离测量的原理、方法和应用。',
          category: 'method'
        },
        { 
          id: '8', 
          text: '控制测量',
          content: '介绍工程控制测量的方法和应用。',
          category: 'application'
        },
        { 
          id: '9', 
          text: '地形测量',
          content: '介绍地形图测绘的方法和应用。',
          category: 'application'
        },
        { 
          id: '10', 
          text: '工程放样',
          content: '介绍土木工程建设中的放样测量方法和应用。',
          category: 'application'
        },
        { 
          id: '11', 
          text: '变形监测',
          content: '介绍工程结构变形监测的方法和应用。',
          category: 'application'
        }
      ],
      lines: [
        { from: '1', to: '2', text: '包含' },
        { from: '1', to: '3', text: '包含' },
        { from: '1', to: '4', text: '包含' },
        { from: '1', to: '5', text: '应用' },
        { from: '1', to: '6', text: '应用' },
        { from: '1', to: '7', text: '应用' },
        { from: '3', to: '5', text: '支持' },
        { from: '3', to: '6', text: '支持' },
        { from: '3', to: '7', text: '支持' },
        { from: '4', to: '5', text: '用于' },
        { from: '4', to: '6', text: '用于' },
        { from: '4', to: '7', text: '用于' },
        { from: '5', to: '8', text: '应用于' },
        { from: '6', to: '8', text: '应用于' },
        { from: '7', to: '8', text: '应用于' },
        { from: '5', to: '9', text: '应用于' },
        { from: '6', to: '9', text: '应用于' },
        { from: '7', to: '9', text: '应用于' },
        { from: '8', to: '10', text: '支持' },
        { from: '9', to: '10', text: '支持' },
        { from: '8', to: '11', text: '支持' }
      ]
    },
    'civil-intro': {
      rootId: '1',
      nodes: [
        { 
          id: '1', 
          text: '土木工程概述',
          content: '介绍土木工程的定义、历史发展和现代土木工程的范畴。',
          category: 'root'
        },
        { 
          id: '2', 
          text: '土木工程的定义与分类',
          content: '土木工程的基本定义、主要分支及其在国民经济中的地位和作用。',
          category: 'concept'
        },
        { 
          id: '3', 
          text: '土木工程发展简史',
          content: '从古至今土木工程的发展历程、重要里程碑及杰出代表人物。',
          category: 'history'
        },
        { 
          id: '4', 
          text: '现代土木工程的特点',
          content: '现代土木工程的跨学科特点、创新趋势及面临的挑战。',
          category: 'concept'
        },
        { 
          id: '5', 
          text: '结构工程基础',
          content: '介绍结构工程的基本概念、结构类型及其设计原则。',
          category: 'branch'
        },
        { 
          id: '6', 
          text: '建筑材料基础',
          content: '介绍土木工程中常用的建筑材料及其性能特点。',
          category: 'branch'
        },
        { 
          id: '7', 
          text: '岩土工程基础',
          content: '介绍岩土工程的基本概念、土壤性质及基础类型。',
          category: 'branch'
        },
        { 
          id: '8', 
          text: '水利工程基础',
          content: '介绍水利工程的基本概念、常见水工建筑物及防洪工程。',
          category: 'branch'
        },
        { 
          id: '9', 
          text: '交通工程基础',
          content: '介绍道路、桥梁、隧道等交通工程的基本知识。',
          category: 'branch'
        },
        { 
          id: '10', 
          text: '施工技术与管理',
          content: '介绍土木工程施工的基本技术和项目管理知识。',
          category: 'application'
        },
        { 
          id: '11', 
          text: '土木工程与可持续发展',
          content: '介绍土木工程与可持续发展的关系及绿色建筑理念。',
          category: 'application'
        }
      ],
      lines: [
        { from: '1', to: '2', text: '定义' },
        { from: '1', to: '3', text: '历史' },
        { from: '1', to: '4', text: '特点' },
        { from: '1', to: '5', text: '分支' },
        { from: '1', to: '6', text: '分支' },
        { from: '1', to: '7', text: '分支' },
        { from: '1', to: '8', text: '分支' },
        { from: '1', to: '9', text: '分支' },
        { from: '5', to: '10', text: '应用' },
        { from: '6', to: '10', text: '应用' },
        { from: '7', to: '10', text: '应用' },
        { from: '8', to: '10', text: '应用' },
        { from: '9', to: '10', text: '应用' },
        { from: '5', to: '11', text: '影响' },
        { from: '6', to: '11', text: '影响' },
        { from: '7', to: '11', text: '影响' },
        { from: '8', to: '11', text: '影响' },
        { from: '9', to: '11', text: '影响' }
      ]
    },
    'structural-analysis': {
      rootId: '1',
      nodes: [
        { 
          id: '1', 
          text: '结构力学基础',
          content: '结构力学的基本概念、原理和方法。',
          category: 'root'
        },
        { 
          id: '2', 
          text: '静力学基本原理',
          content: '力的概念、力的分解与合成、力矩、力偶等基本原理。',
          category: 'principle'
        },
        { 
          id: '3', 
          text: '结构模型与荷载',
          content: '结构的理想化模型、荷载类型及其作用特点。',
          category: 'concept'
        },
        { 
          id: '4', 
          text: '平面体系的平衡',
          content: '平面力系的平衡条件及应用。',
          category: 'principle'
        },
        { 
          id: '5', 
          text: '平面桁架',
          content: '桁架的类型、特点及内力计算方法。',
          category: 'structure'
        },
        { 
          id: '6', 
          text: '梁的内力分析',
          content: '梁的弯矩、剪力计算及内力图绘制方法。',
          category: 'analysis'
        },
        { 
          id: '7', 
          text: '梁的变形计算',
          content: '梁的挠度和转角计算方法。',
          category: 'analysis'
        },
        { 
          id: '8', 
          text: '虚功原理',
          content: '虚位移原理、虚力原理及其在结构分析中的应用。',
          category: 'principle'
        },
        { 
          id: '9', 
          text: '影响线',
          content: '影响线的概念、绘制方法及应用。',
          category: 'method'
        },
        { 
          id: '10', 
          text: '超静定结构分析',
          content: '力法、位移法等超静定结构的分析方法。',
          category: 'method'
        },
        { 
          id: '11', 
          text: '矩阵结构分析',
          content: '基于矩阵方法的结构分析理论与应用。',
          category: 'method'
        }
      ],
      lines: [
        { from: '1', to: '2', text: '基础' },
        { from: '1', to: '3', text: '基础' },
        { from: '1', to: '4', text: '应用' },
        { from: '2', to: '4', text: '支持' },
        { from: '2', to: '5', text: '应用于' },
        { from: '4', to: '5', text: '分析' },
        { from: '2', to: '6', text: '应用于' },
        { from: '4', to: '6', text: '分析' },
        { from: '6', to: '7', text: '基础' },
        { from: '8', to: '7', text: '方法' },
        { from: '8', to: '9', text: '支持' },
        { from: '8', to: '10', text: '支持' },
        { from: '10', to: '11', text: '发展' },
        { from: '1', to: '8', text: '原理' },
        { from: '1', to: '9', text: '方法' },
        { from: '1', to: '10', text: '方法' },
        { from: '1', to: '11', text: '方法' }
      ]
    },
    'construction-materials': {
      rootId: '1',
      nodes: [
        { 
          id: '1', 
          text: '建筑材料',
          content: '建筑材料的性质和应用。',
          category: 'root'
        },
        { 
          id: '2', 
          text: '材料基本性质',
          content: '材料的物理、化学和力学性质。',
          category: 'property'
        },
        { 
          id: '3', 
          text: '混凝土',
          content: '混凝土的组成、性能、配合比设计及施工工艺。',
          category: 'material'
        },
        { 
          id: '4', 
          text: '钢材',
          content: '钢材的类型、性能及在工程中的应用。',
          category: 'material'
        },
        { 
          id: '5', 
          text: '砌体材料',
          content: '砖、砌块等砌体材料的特性及应用。',
          category: 'material'
        },
        { 
          id: '6', 
          text: '木材',
          content: '木材的结构、性能及在建筑中的应用。',
          category: 'material'
        },
        { 
          id: '7', 
          text: '高分子材料',
          content: '塑料、橡胶等高分子材料在建筑中的应用。',
          category: 'material'
        },
        { 
          id: '8', 
          text: '防水材料',
          content: '各类防水材料的性能和应用。',
          category: 'material'
        },
        { 
          id: '9', 
          text: '保温隔热材料',
          content: '保温隔热材料的种类、性能和应用。',
          category: 'material'
        },
        { 
          id: '10', 
          text: '装饰材料',
          content: '各类装饰材料的性能和应用。',
          category: 'material'
        },
        { 
          id: '11', 
          text: '绿色建材',
          content: '环保、节能、可持续的绿色建筑材料。',
          category: 'material'
        }
      ],
      lines: [
        { from: '1', to: '2', text: '特性' },
        { from: '1', to: '3', text: '包含' },
        { from: '1', to: '4', text: '包含' },
        { from: '1', to: '5', text: '包含' },
        { from: '1', to: '6', text: '包含' },
        { from: '1', to: '7', text: '包含' },
        { from: '1', to: '8', text: '包含' },
        { from: '1', to: '9', text: '包含' },
        { from: '1', to: '10', text: '包含' },
        { from: '1', to: '11', text: '发展' },
        { from: '2', to: '3', text: '决定' },
        { from: '2', to: '4', text: '决定' },
        { from: '2', to: '5', text: '决定' },
        { from: '2', to: '6', text: '决定' },
        { from: '2', to: '7', text: '决定' },
        { from: '2', to: '8', text: '决定' },
        { from: '2', to: '9', text: '决定' },
        { from: '2', to: '10', text: '决定' },
        { from: '3', to: '11', text: '发展' },
        { from: '4', to: '11', text: '发展' },
        { from: '5', to: '11', text: '发展' },
        { from: '7', to: '11', text: '发展' }
      ]
    },
    'default': {
      rootId: '1',
      nodes: [
        { id: '1', text: '默认知识图谱', category: 'root' },
        { id: '2', text: '知识点1', category: 'concept' },
        { id: '3', text: '知识点2', category: 'concept' }
      ],
      lines: [
        { from: '1', to: '2', text: '包含' },
        { from: '1', to: '3', text: '包含' }
      ]
    }
  };

  return graphs[courseId] || graphs['default'];
};

/**
 * 创建新课程
 * @param {Object} courseData 课程数据
 * @returns {Promise<Object>} 创建结果
 */
export const createCourse = (courseData) => {
  console.log('创建新课程:', courseData);
  return request({
    url: '/core/zstp',
    method: 'post',
    data: courseData
  }).then(response => {
    console.log('创建课程成功:', response);
    return response;
  }).catch(error => {
    console.error('创建课程失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新课程信息
 * @param {Object} courseData 更新的课程数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateCourse = (courseData) => {
  console.log('更新课程信息:', courseData);
  return request({
    url: `/core/zstp`,
    method: 'put',
    data: courseData
  }).then(response => {
    console.log('更新课程成功:', response);
    return response;
  }).catch(error => {
    console.error('更新课程失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除课程
 * @param {string} courseId 课程ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteCourse = (courseId) => {
  console.log(`删除课程: ${courseId}`);
  return request({
    url: `/core/zstp/${courseId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除课程 ${courseId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除课程 ${courseId} 失败:`, error);
    return Promise.reject(error);
  });
}; 