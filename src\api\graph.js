import request from './axios';

/**
 * 获取知识图谱列表
 * @returns {Promise<Array>} 图谱列表
 */
export const getGraphList = () => {
  console.log('正在获取知识图谱列表...');
  return request({
    url: '/core/zstp/list',
    method: 'get'
  }).then(response => {
    console.log('获取知识图谱列表成功:', response);
    // 处理嵌套的响应结构
    if (response && typeof response === 'object') {
      if (response.rows && Array.isArray(response.rows)) {
        console.log('返回标准响应格式');
        return response;
      } else if (Array.isArray(response)) {
        console.log('返回数组格式');
        return { rows: response };
      } else if (response.data && Array.isArray(response.data)) {
        console.log('从嵌套响应中提取图谱列表数据');
        return { rows: response.data };
      }
    }
    console.warn('响应格式不符合预期，返回空数组');
    return { rows: [] };
  }).catch(error => {
    console.error('获取图谱列表失败:', error);
    return { rows: [] };
  });
};

/**
 * 获取指定ID的知识图谱
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 图谱数据
 */
export const getGraph = (graphId) => {
  return request({
    url: `/core/zstp/${graphId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 创建新的知识图谱
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 创建结果
 */
export const createGraph = (graphData) => {
  console.log('正在创建知识图谱:', graphData);
  return request({
    url: '/core/zstp',
    method: 'post',
    data: graphData
  }).then(response => {
    console.log('创建图谱成功:', response);
    return response;
  }).catch(error => {
    console.error('创建图谱失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 更新知识图谱
 * @param {string} graphId 图谱ID
 * @param {Object} graphData 图谱数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateGraph = ( graphData) => {
  console.log(`更新图谱`, graphData);
  return request({
    url: `/core/zstp`,
    method: 'put',
    data: graphData
  }).then(response => {
    console.log(`更新图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`更新图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 删除知识图谱
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteGraph = (graphId) => {
  console.log(`删除图谱: ${graphId}`);
  return request({
    url: `/core/zstp/${graphId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除图谱 ${graphId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除图谱 ${graphId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取知识图谱节点列表
 * @param {string} graphId 图谱ID
 * @returns {Promise<Object>} 节点列表
 */
export const getNodeList = (graphId) => {
  console.log(`正在获取节点数据: ${graphId}`);
  return request({
    url: `core/node/list`,
    method: 'get',
    params: {
      graphId: graphId
    }
  }).then(response => {
    console.log(`获取节点数据:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点列表失败: ${graphId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点样式
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 节点样式
 */
export const getNodeStyle = (nodeId) => {
  return request({
    url: `core/style/${nodeId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取节点样式:`, response);
    return response.data;
  }).catch(error => {
    console.error(`获取节点样式失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 获取节点连线
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 连线列表
 */
export const getNodeLines = (nodeId) => {
  return request({
    url: `core/line/${nodeId}`,
    method: 'get',
    params: {
      nodeId: nodeId
    }
  }).then(response => {
    console.log(`获取节点线条:`, response);
    return response.rows;
  }).catch(error => {
    console.error(`获取节点线条失败: ${nodeId}`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点数据
 * @param {Object} nodeData 节点数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNode = (nodeData) => {
  return request({
    url: '/core/node',
    method: 'post',
    data: nodeData
  }).then(response => {
    console.log('保存节点成功:', response);
    return response;
  }).catch(error => {
    console.error('保存节点失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除节点
 * @param {string} nodeId 节点ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteNode = (nodeId) => {
  return request({
    url: `/core/node/${nodeId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除节点 ${nodeId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除节点 ${nodeId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存连线数据
 * @param {Object} lineData 连线数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveLine = (lineData) => {
  return request({
    url: '/core/line',
    method: 'post',
    data: lineData
  }).then(response => {
    console.log('保存连线成功:', response);
    return response;
  }).catch(error => {
    console.error('保存连线失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 删除连线
 * @param {string} lineId 连线ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteLine = (lineId) => {
  return request({
    url: `/core/line/${lineId}`,
    method: 'delete'
  }).then(response => {
    console.log(`删除连线 ${lineId} 成功:`, response);
    return response;
  }).catch(error => {
    console.error(`删除连线 ${lineId} 失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 保存节点样式
 * @param {Object} styleData 样式数据
 * @returns {Promise<Object>} 保存结果
 */
export const saveNodeStyle = (styleData) => {
  return request({
    url: '/core/style',
    method: 'post',
    data: styleData
  }).then(response => {
    console.log('保存样式成功:', response);
    return response;
  }).catch(error => {
    console.error('保存样式失败:', error);
    return Promise.reject(error);
  });
};

// 兼容courses.js中的命名
export const getCourseList = getGraphList;
export const getCourseGraph = getNodeList; 